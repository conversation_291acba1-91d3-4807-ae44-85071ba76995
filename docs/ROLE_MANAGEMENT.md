# Secure Role Management System

This document explains the new secure role management system that replaces localStorage-based role storage with API-based role fetching.

## Overview

The new system provides:
- **Security**: User roles are fetched from the server, preventing client-side manipulation
- **Performance**: Intelligent caching with stale time to minimize API calls
- **Reliability**: Automatic role synchronization with the backend
- **Consistency**: Single source of truth for user roles

## Key Components

### 1. UserRoleProvider (`src/providers/UserRoleProvider.tsx`)
- Provides user role context throughout the application
- Automatically initializes PermissionManager with the current role
- Handles role loading states and errors

### 2. useUserRole Hook (`src/hooks/useUserRole.ts`)
- Fetches user role from `/auth/me` API endpoint
- Implements 10-minute stale time for optimal performance
- Maps backend roles to frontend role types
- Provides loading states and error handling

### 3. useAuthWithRole Hook
- Combines authentication status with role information
- Provides a single hook for checking auth + role status
- Returns `isReady` flag when both auth and role are available

### 4. Enhanced QueryClient (`src/app/QueryProvider.tsx`)
- Configured with optimized defaults for user data
- 5-minute default stale time for all queries
- Smart retry logic that doesn't retry on auth errors

## Usage Examples

### Basic Role Checking
```tsx
import { useAuthWithRole } from '@/providers/UserRoleProvider';

function MyComponent() {
  const { role, isLoading, isReady } = useAuthWithRole();

  if (isLoading) return <div>Loading...</div>;
  if (!isReady) return <div>Not authenticated</div>;

  return <div>Welcome, {role}!</div>;
}
```

### Permission-Based Rendering
```tsx
import { usePermissionsWithRole } from '@/lib/permissions';

function AdminPanel() {
  const { hasPermission, role } = usePermissionsWithRole();

  if (!hasPermission('MANAGE_EMPLOYERS')) {
    return <div>Access denied</div>;
  }

  return <div>Admin content for {role}</div>;
}
```

### Logout with Cache Clearing
```tsx
import { useLogout } from '@/hooks/useLogout';

function LogoutButton() {
  const { logout } = useLogout();

  return <button onClick={logout}>Logout</button>;
}
```

## Migration from localStorage

### Before (Insecure)
```tsx
// ❌ Insecure - can be manipulated by users
const userRole = localStorage.getItem('userRole');
```

### After (Secure)
```tsx
// ✅ Secure - fetched from server with caching
const { role } = useAuthWithRole();
```

## Caching Strategy

- **User Role Query**: 10-minute stale time, 15-minute garbage collection
- **Default Queries**: 5-minute stale time
- **No Window Focus Refetch**: Prevents unnecessary API calls
- **Smart Retry Logic**: Doesn't retry on 401/403 errors

## Error Handling

The system handles various error scenarios:
- **401/403 Errors**: Automatic logout and redirect to login
- **Network Errors**: Retry with exponential backoff
- **Invalid Roles**: Redirect to login page
- **Missing Permissions**: Graceful access denial

## Performance Optimizations

1. **Stale-While-Revalidate**: Users see cached data immediately while fresh data loads in background
2. **Conditional Fetching**: Role queries only run when user is authenticated
3. **Garbage Collection**: Automatic cleanup of unused cache entries
4. **Selective Invalidation**: Only relevant queries are invalidated on login/logout

## Security Benefits

1. **Server-Side Validation**: Roles are always validated by the backend
2. **Tamper-Proof**: Users cannot modify their roles client-side
3. **Session Consistency**: Roles are automatically updated if changed server-side
4. **Audit Trail**: All role checks go through the API, enabling server-side logging

## Troubleshooting

### Role Not Loading
- Check if user is authenticated (`authToken` exists)
- Verify `/auth/me` API endpoint is working
- Check network tab for API errors

### Permission Denied
- Ensure PermissionManager is initialized with correct role
- Verify role mapping in `useUserRole` hook
- Check if user has required permissions for the route

### Infinite Loading
- Check if `UserRoleProvider` is properly wrapped around the app
- Verify React Query is configured correctly
- Ensure auth token is available when role query runs
