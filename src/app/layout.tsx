import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { GeistSans } from 'geist/font/sans';
import './globals.css';
import { QueryProvider } from './QueryProvider';
import { UserRoleProvider } from '@/providers/UserRoleProvider';
import { Suspense } from 'react';
import { Toaster } from '@/components/ui/sonner';
import NextTopLoader from 'nextjs-toploader';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Finwage',
  // description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${GeistSans.className} ${inter.className}`}>
        <NextTopLoader color="#F74464" showSpinner={false} />
        <QueryProvider>
          <UserRoleProvider>
            <Suspense>{children}</Suspense>
          </UserRoleProvider>
        </QueryProvider>
        <Toaster />
      </body>
    </html>
  );
}
