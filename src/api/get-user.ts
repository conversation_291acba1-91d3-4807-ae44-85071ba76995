import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/store/auth-store';
import { useEffect } from 'react';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
}

export interface GetUserResponse {
  statusCode: number;
  message: string;
  data: {
    user: User;
  };
  timestamp: string;
  path: string;
  method: string;
}

const getMe = (): Promise<GetUserResponse> => {
  return apiClient.get('/auth/me');
};

export const useGetMe = () => {
  const { setUserDetails, authToken } = useAuthStore();

  const query = useQuery({
    queryKey: ['me'],
    queryFn: getMe,
    enabled: !!authToken, // Only fetch when authenticated
    staleTime: 10 * 60 * 1000, // 10 minutes stale time for user data
  });

  // Store user details in auth store when data is successfully fetched
  useEffect(() => {
    if (query.data?.data?.user) {
      setUserDetails(query.data.data.user);
    }
  }, [query.data, setUserDetails]);

  return query;
};
