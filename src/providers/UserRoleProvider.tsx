'use client';

import React, { createContext, useContext, useEffect } from 'react';
import { useUserRole } from '@/hooks/useUserRole';
import { PermissionManager, type Role } from '@/lib/permissions';
import { useAuthStore } from '@/store/auth-store';

interface UserRoleContextType {
  role: Role | null;
  isLoading: boolean;
  error: any;
  hasRequiredRole: boolean;
  refetch: () => void;
}

const UserRoleContext = createContext<UserRoleContextType | undefined>(undefined);

interface UserRoleProviderProps {
  children: React.ReactNode;
}

export function UserRoleProvider({ children }: UserRoleProviderProps) {
  const { authToken } = useAuthStore();
  const { role, isLoading, error, hasRequiredRole, refetch } = useUserRole();

  // Initialize PermissionManager when role changes
  useEffect(() => {
    if (role && authToken) {
      const permissionManager = PermissionManager.getInstance();
      permissionManager.setUserRole(role);
    }
  }, [role, authToken]);

  // Clear role when user logs out
  useEffect(() => {
    if (!authToken) {
      const permissionManager = PermissionManager.getInstance();
      // Reset permission manager when no auth token
      permissionManager.setUserRole(null as any);
    }
  }, [authToken]);

  const contextValue: UserRoleContextType = {
    role,
    isLoading,
    error,
    hasRequiredRole,
    refetch,
  };

  return (
    <UserRoleContext.Provider value={contextValue}>
      {children}
    </UserRoleContext.Provider>
  );
}

export function useUserRoleContext() {
  const context = useContext(UserRoleContext);
  if (context === undefined) {
    throw new Error('useUserRoleContext must be used within a UserRoleProvider');
  }
  return context;
}

// Convenience hook that combines auth and role checking
export function useAuthWithRole() {
  const { authToken } = useAuthStore();
  const { role, isLoading, hasRequiredRole } = useUserRoleContext();

  return {
    isAuthenticated: !!authToken,
    role,
    isLoading,
    hasRequiredRole,
    isReady: !!authToken && !isLoading && !!role,
  };
}
