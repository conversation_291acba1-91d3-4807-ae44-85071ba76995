import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import { useAuthStore } from '@/store/auth-store';
import { type Role } from '@/lib/permissions';

export interface UserRoleResponse {
  statusCode: number;
  message: string;
  data: {
    user: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      roles: string[];
    };
  };
  timestamp: string;
  path: string;
  method: string;
}

const fetchUserRole = async (): Promise<UserRoleResponse> => {
  const response = await apiClient.get('/auth/me');
  return response as unknown as UserRoleResponse;
};

const mapRolesToUserRole = (roles: string[]): Role | null => {
  // Determine user role based on priority (same logic as login)
  if (roles.includes('SUPER_ADMIN')) {
    return 'super_admin';
  } else if (roles.includes('EMPLOYER')) {
    return 'employer';
  } else if (roles.includes('SUB_ADMIN')) {
    return 'sub_admin';
  }
  return null;
};

export const useUserRole = () => {
  const { authToken } = useAuthStore();

  const query = useQuery({
    queryKey: ['user-role'],
    queryFn: fetchUserRole,
    enabled: !!authToken, // Only fetch when user is authenticated
    staleTime: 10 * 60 * 1000, // 10 minutes - longer stale time for role data
    gcTime: 15 * 60 * 1000, // 15 minutes garbage collection time
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    select: (data) => {
      const userRole = mapRolesToUserRole(data.data.user.roles);
      return {
        role: userRole,
        user: data.data.user,
        hasRequiredRole: userRole !== null,
      };
    },
  });

  return {
    ...query,
    role: query.data?.role || null,
    user: query.data?.user || null,
    hasRequiredRole: query.data?.hasRequiredRole || false,
  };
};

// Hook for getting role synchronously (when already cached)
export const useUserRoleSync = () => {
  const { authToken } = useAuthStore();
  
  const query = useQuery({
    queryKey: ['user-role'],
    queryFn: fetchUserRole,
    enabled: false, // Don't auto-fetch
    staleTime: 10 * 60 * 1000,
  });

  // Return cached data if available
  if (query.data && authToken) {
    const userRole = mapRolesToUserRole(query.data.data.user.roles);
    return {
      role: userRole,
      user: query.data.data.user,
      hasRequiredRole: userRole !== null,
      isLoading: false,
      error: null,
    };
  }

  return {
    role: null,
    user: null,
    hasRequiredRole: false,
    isLoading: query.isLoading,
    error: query.error,
  };
};
