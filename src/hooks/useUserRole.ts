import { useGetMe } from '@/api/get-user';
import { type Role } from '@/lib/permissions';

const mapRolesToUserRole = (roles: string[]): Role | null => {
  // Determine user role based on priority (same logic as login)
  if (roles.includes('SUPER_ADMIN')) {
    return 'super_admin';
  } else if (roles.includes('EMPLOYER')) {
    return 'employer';
  } else if (roles.includes('SUB_ADMIN')) {
    return 'sub_admin';
  }
  return null;
};

export const useUserRole = () => {
  const { data, isLoading, error } = useGetMe();

  const user = data?.data?.user;
  const role = user ? mapRolesToUserRole(user.roles) : null;
  const hasRequiredRole = role !== null;

  return {
    role,
    user,
    hasRequiredRole,
    isLoading,
    error,
  };
};
