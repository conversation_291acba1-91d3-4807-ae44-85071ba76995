import { useAuthStore } from '@/store/auth-store';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { PermissionManager } from '@/lib/permissions';

export const useLogout = () => {
  const { logout } = useAuthStore();
  const queryClient = useQueryClient();
  const router = useRouter();

  const handleLogout = () => {
    // Clear auth store
    logout();

    // Clear all React Query cache
    queryClient.clear();

    // Reset permission manager
    const permissionManager = PermissionManager.getInstance();
    permissionManager.setUserRole(null as any);

    // Redirect to login
    router.push('/');
  };

  return { logout: handleLogout };
};
