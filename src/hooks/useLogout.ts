import { useAuthStore } from '@/store/auth-store';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { PermissionManager } from '@/lib/permissions';

export const useLogout = () => {
  const { logout } = useAuthStore();
  const queryClient = useQueryClient();

  const handleLogout = () => {
    // Clear all React Query cache
    queryClient.clear();

    // Reset permission manager
    const permissionManager = PermissionManager.getInstance();
    permissionManager.setUserRole(null as any);

    // Clear auth store
    logout();
    // Redirect to login
    window.location.href = '/';
  };

  return { logout: handleLogout };
};
