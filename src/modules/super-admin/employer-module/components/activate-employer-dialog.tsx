'use client';

import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { useReactivateEmployer } from '../api/useReactivateEmployer';
import { Employer } from '../api/useEmployers';

interface ActivateEmployerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  employer: Employer | null;
}

export const ActivateEmployerDialog = ({
  isOpen,
  onClose,
  employer,
}: ActivateEmployerDialogProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutateAsync: reactivateEmployer } = useReactivateEmployer();

  const handleActivate = async () => {
    if (!employer) return;

    setIsSubmitting(true);
    try {
      await reactivateEmployer(employer.id);
      toast.success(`${employer.name} has been activated successfully.`);
      onClose();
    } catch (error) {
      toast.error('Failed to activate employer. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Activate Employer</DialogTitle>
          <DialogDescription>
            Are you sure you want to activate <strong>{employer?.name}</strong>? This will restore
            their access to the system.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="gap-2">
          <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="button" onClick={handleActivate} disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Activate Employer
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
