'use client';

import { useEmployer } from '../api/useEmployer';
import { EmployerDetail } from './employer-details';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

interface EmployerDetailViewProps {
  employerId: string;
}

export const EmployerDetailView = ({ employerId }: EmployerDetailViewProps) => {
  const { data: employerResponse, isLoading, error } = useEmployer(employerId);

  if (isLoading) {
    return (
      <div className="flex h-[50vh] items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="text-lg">Loading employer details...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="mx-auto max-w-md">
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-red-600">Error Loading Employer</h3>
            <p className="mt-2 text-sm text-gray-600">
              Unable to load employer details. Please try again later.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!employerResponse?.data) {
    return (
      <Card className="mx-auto max-w-md">
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900">Employer Not Found</h3>
            <p className="mt-2 text-sm text-gray-600">The requested employer could not be found.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Transform API data to match the component's expected format
  const employer = {
    id: employerResponse.data.id,
    companyName: employerResponse.data.name,
    contactPerson: `${employerResponse.data.contactPerson.firstName} ${employerResponse.data.contactPerson.lastName}`,
    email: employerResponse.data.contactPerson.email,
    status: employerResponse.data.status.toLowerCase(),
    phone: employerResponse.data.phoneNumber,
    address: employerResponse.data.address,
    employeeCount: 12, // This will be static for now as mentioned
    createdAt: employerResponse.data.createdAt,
    updatedAt: employerResponse.data.updatedAt,
  };

  return <EmployerDetail employer={employer} rawEmployerData={employerResponse.data} />;
};
