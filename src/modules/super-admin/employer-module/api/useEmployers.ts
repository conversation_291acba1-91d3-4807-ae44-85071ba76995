import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export type EmployerStatus = 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'REJECTED';

export interface ContactPerson {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface Employer {
  id: string;
  name: string;
  contactPerson: ContactPerson;
  phoneNumber: string;
  address: string;
  status: EmployerStatus;
  createdAt: string;
  updatedAt: string;
}

export interface EmployersResponse {
  statusCode: number;
  message: string;
  data: Employer[];
  meta: {
    total: number;
    lastPage: number;
    currentPage: number;
    perPage: number;
    prev: number | null;
    next: number | null;
  };
  timestamp: string;
  path: string;
  method: string;
}

export interface EmployersFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: EmployerStatus | 'all';
}

const fetchEmployers = async (filters: EmployersFilters = {}): Promise<EmployersResponse> => {
  const params = new URLSearchParams();

  // Add pagination parameters
  if (filters.page) {
    params.append('page', filters.page.toString());
  }
  if (filters.limit) {
    params.append('limit', filters.limit.toString());
  }

  // Add filter parameters
  if (filters.search && filters.search.trim()) {
    params.append('search', filters.search.trim());
  }
  if (filters.status && filters.status !== 'all') {
    params.append('status', filters.status);
  }

  const queryString = params.toString();
  const url = queryString ? `/employers?${queryString}` : '/employers';

  const response = await apiClient.get(url);

  // The API client's response interceptor returns response.data,
  // so response is already the API response body
  return response as unknown as EmployersResponse;
};

export const useEmployers = (filters: EmployersFilters = {}) => {
  return useQuery({
    queryKey: ['employers-list', filters],
    queryFn: () => fetchEmployers(filters),
  });
};
