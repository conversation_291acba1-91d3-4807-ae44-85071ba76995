import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface ReactivateEmployerResponse {
  statusCode: number;
  message: string;
  data: any;
  timestamp: string;
  path: string;
  method: string;
}

const reactivateEmployerApi = async (id: string): Promise<ReactivateEmployerResponse> => {
  const response = await apiClient.post(`/employers/${id}/reactivate`);
  return response as unknown as ReactivateEmployerResponse;
};

export const useReactivateEmployer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => reactivateEmployerApi(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employers-list'] });
    },
  });
};
