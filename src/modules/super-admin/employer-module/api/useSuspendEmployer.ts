import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface SuspendEmployerPayload {
  reason: string;
}

interface SuspendEmployerResponse {
  statusCode: number;
  message: string;
  data: any;
  timestamp: string;
  path: string;
  method: string;
}

const suspendEmployerApi = async (
  id: string,
  payload: SuspendEmployerPayload
): Promise<SuspendEmployerResponse> => {
  const response = await apiClient.post(`/employers/${id}/suspend`, payload);
  return response as unknown as SuspendEmployerResponse;
};

export const useSuspendEmployer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) =>
      suspendEmployerApi(id, { reason }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employers-list'] });
    },
  });
};
