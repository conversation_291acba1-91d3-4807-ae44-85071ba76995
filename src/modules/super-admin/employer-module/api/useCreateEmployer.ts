import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

export type CreateEmployerPayload = {
  companyName: string;
  contactPersonFirstName: string;
  contactPersonLastName: string;
  email: string;
  phoneNumber: string;
  address: string;
};

const createEmployerApi = async (payload: CreateEmployerPayload) => {
  const response = await apiClient.post(`/employers`, payload);
  return response.data;
};

export const useCreateEmployer = () => {
  return useMutation({ mutationFn: createEmployerApi });
};
