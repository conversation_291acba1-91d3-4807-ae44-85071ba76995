import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';

export type UpdateEmployerPayload = {
  companyName: string;
  contactPersonFirstName: string;
  contactPersonLastName: string;
  email: string;
  phoneNumber: string;
  address: string;
};

const updateEmployerApi = async (id: string, payload: UpdateEmployerPayload) => {
  const response = await apiClient.put(`/employers/${id}`, payload);
  return response.data;
};

export const useUpdateEmployer = () => {
  return useMutation({
    mutationFn: ({ id, ...payload }: UpdateEmployerPayload & { id: string }) =>
      updateEmployerApi(id, payload),
  });
};
