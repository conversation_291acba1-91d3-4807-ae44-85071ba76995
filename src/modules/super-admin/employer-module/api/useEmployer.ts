import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';

export interface EmployerContactPerson {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export interface EmployerDetail {
  id: string;
  name: string;
  contactPerson: EmployerContactPerson;
  phoneNumber: string;
  address: string;
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'REJECTED';
  createdAt: string;
  updatedAt: string;
}

export interface EmployerDetailResponse {
  statusCode: number;
  message: string;
  data: EmployerDetail;
  timestamp: string;
  path: string;
  method: string;
}

const fetchEmployer = async (id: string): Promise<EmployerDetailResponse> => {
  const response = await apiClient.get(`/employers/${id}`);

  // The API client's response interceptor returns response.data,
  // so response is already the API response body
  return response as unknown as EmployerDetailResponse;
};

export const useEmployer = (id: string) => {
  return useQuery({
    queryKey: ['employer-details', id],
    queryFn: () => fetchEmployer(id),
    enabled: !!id, // Only run query if id is provided
  });
};
