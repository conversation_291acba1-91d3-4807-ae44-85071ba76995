import { <PERSON>, CardContent } from '@/components/ui/card';
import { <PERSON>R<PERSON>, Eye, Lock, Mail } from 'lucide-react';

const AuthRightLayout = () => {
  return (
    <div className="flex w-3/6 items-center justify-center bg-gradient-to-br from-[#F74464] to-[#F74464]/80 p-12">
      <div className="max-w-2xl space-y-8 text-center text-white">
        <div className="space-y-4">
          <h1 className="text-5xl font-bold">Admin Dashboard</h1>
          <p className="text-xl text-white/90">
            Streamline your business operations with our comprehensive management platform
          </p>
        </div>

        <div className="mt-12 grid grid-cols-2 gap-6">
          <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-white/20">
                <Mail className="h-6 w-6 text-white" />
              </div>
              <h3 className="mb-2 font-semibold text-white">Employee Management</h3>
              <p className="text-sm text-white/80">Manage your workforce efficiently</p>
            </CardContent>
          </Card>

          <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-white/20">
                <Lock className="h-6 w-6 text-white" />
              </div>
              <h3 className="mb-2 font-semibold text-white">Secure Access</h3>
              <p className="text-sm text-white/80">Enterprise-grade security</p>
            </CardContent>
          </Card>

          <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-white/20">
                <ArrowRight className="h-6 w-6 text-white" />
              </div>
              <h3 className="mb-2 font-semibold text-white">Real-time Analytics</h3>
              <p className="text-sm text-white/80">Track performance metrics</p>
            </CardContent>
          </Card>

          <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
            <CardContent className="p-6 text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-white/20">
                <Eye className="h-6 w-6 text-white" />
              </div>
              <h3 className="mb-2 font-semibold text-white">Easy Integration</h3>
              <p className="text-sm text-white/80">Connect with your tools</p>
            </CardContent>
          </Card>
        </div>

        <div className="mt-12">
          <p className="text-white/70">
            Trusted by thousands of businesses worldwide to manage their operations effectively
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthRightLayout;
