'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { type Role } from '@/lib/permissions';
import { useAuthStore } from '@/store/auth-store';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowRight, Eye, EyeOff, Lock, Mail } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import AuthRightLayout from '../layout';
import { useLogin } from '../api/useLogin';
import { toast } from '@/lib/toast';
import { useRouter } from 'nextjs-toploader/app';
import Image from 'next/image';
import { useQueryClient } from '@tanstack/react-query';

const loginSchema = z.object({
  email: z.email('Please enter a valid email address'),
  password: z.string().min(1, {
    message: 'Password is required.',
  }),
  rememberMe: z.boolean().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

export function LoginPage() {
  const { setToken, setUserId, setUserDetails } = useAuthStore();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const queryClient = useQueryClient();

  //api
  const { mutateAsync: loginMutate } = useLogin();

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  const handleLoginSuccess = (responseData: any) => {
    // Check if user has required admin roles
    const roles = responseData.user.roles || [];
    const hasRequiredRole =
      roles.includes('SUPER_ADMIN') || roles.includes('EMPLOYER') || roles.includes('SUB_ADMIN');

    if (!hasRequiredRole) {
      toast.error("You don't have admin credentials. Access denied.");
      setIsLoading(false);
      return;
    }

    // Determine user role based on priority
    let userRole: Role;

    if (roles.includes('SUPER_ADMIN')) {
      userRole = 'super_admin';
    } else if (roles.includes('EMPLOYER')) {
      userRole = 'employer';
    } else {
      // Since we've already checked hasRequiredRole, this must be SUB_ADMIN
      userRole = 'sub_admin';
    }

    setToken(responseData.accessToken, responseData.refreshToken);
    setUserId(responseData.user.id);
    setUserDetails({
      id: responseData.user.id,
      firstName: responseData.user.firstName,
      lastName: responseData.user.lastName || '',
      email: responseData.user.email,
    });

    // Invalidate user role query to fetch fresh data
    queryClient.invalidateQueries({ queryKey: ['user-role'] });

    // Redirect based on role
    if (userRole === 'super_admin') {
      router.push('/admin/employers');
    } else {
      // Both sub_admin and employer use the same dashboard initially
      router.push('/employer/dashboard');
    }

    toast.success('Login successful!');

    setIsLoading(false);
  };

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);

    const params = {
      email: data.email,
      password: data.password,
    };

    loginMutate(params, {
      onSuccess: (response) => {
        handleLoginSuccess(response);
      },
      onError: (error) => {
        toast.error(error);
        setIsLoading(false);
      },
    });
  };

  return (
    <div className="flex min-h-screen">
      {/* Left Side - Branding (60%) */}
      <AuthRightLayout />

      {/* Right Side - Login Form (40%) */}
      <div className="flex w-3/6 items-center justify-center bg-white p-8">
        <div className="w-full max-w-md space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="flex justify-center pb-3">
              <Image
                src="/logo_finwage.svg"
                alt="FinWage Logo"
                width={120}
                height={60}
                className="h-16 w-auto"
                priority
              />
            </div>
            <h2 className="text-3xl font-bold text-gray-900">Welcome Back</h2>
            <p className="mt-2 text-gray-600">Sign in to your account to continue</p>
          </div>

          {/* Demo Credentials - Removed Admin, Added Super Admin */}
          {/* <div className="grid grid-cols-3 gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => fillDemoCredentials('super_admin')}
              className="text-xs"
            >
              Super Admin
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => fillDemoCredentials('sub_admin')}
              className="text-xs"
            >
              Sub Admin
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => fillDemoCredentials('employer')}
              className="text-xs"
            >
              Employer
            </Button>
          </div> */}

          {/* Login Form */}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Email Field */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      Email Address
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                        <Input
                          {...field}
                          type="email"
                          placeholder="Enter your email"
                          className="h-12 pl-10"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Password Field */}
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                        <Input
                          {...field}
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Enter your password"
                          className="h-12 pr-10 pl-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute top-1/2 right-3 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between">
                <FormField
                  control={form.control}
                  name="rememberMe"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-sm text-gray-600">Remember me</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                <button type="button" className="text-primary hover:text-primary/80 text-sm">
                  Forgot password?
                </button>
              </div>

              {/* Sign In Button */}
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-primary hover:bg-primary/90 h-12 w-full font-medium text-white"
              >
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    <span>Signing in...</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <span>Sign In</span>
                    <ArrowRight className="h-4 w-4" />
                  </div>
                )}
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
