'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { toast } from '@/lib/toast';
import { useSuspendEmployee } from '../api/useSuspendEmployee';
import { Employee } from '../api/useEmployees';

const suspendSchema = z.object({
  reason: z
    .string()
    .min(10, 'Reason must be at least 10 characters long')
    .max(500, 'Reason must not exceed 500 characters'),
});

type SuspendFormData = z.infer<typeof suspendSchema>;

interface SuspendEmployeeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  employee: Employee | null;
}

export const SuspendEmployeeDialog = ({
  isOpen,
  onClose,
  employee,
}: SuspendEmployeeDialogProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutateAsync: suspendEmployee } = useSuspendEmployee();

  const form = useForm<SuspendFormData>({
    resolver: zodResolver(suspendSchema),
    defaultValues: {
      reason: '',
    },
  });

  const handleSubmit = async (data: SuspendFormData) => {
    if (!employee) return;

    setIsSubmitting(true);
    try {
      await suspendEmployee({
        id: employee.id,
        reason: data.reason,
      });

      toast.success(`${employee.fullName} has been suspended successfully.`);
      form.reset();
      onClose();
    } catch (error) {
      toast.error('Failed to suspend employee. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Suspend Employee</DialogTitle>
          <DialogDescription>
            Are you sure you want to suspend <strong>{employee?.fullName}</strong>? Please provide a
            reason for suspension.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Reason for Suspension <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter the reason for suspending this employee..."
                      className="min-h-[100px] resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" variant="destructive" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Suspend Employee
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
