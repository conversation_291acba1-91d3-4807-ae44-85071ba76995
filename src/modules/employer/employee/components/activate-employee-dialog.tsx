'use client';

import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { useReactivateEmployee } from '../api/useReactivateEmployee';
import { Employee } from '../api/useEmployees';

interface ActivateEmployeeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  employee: Employee | null;
}

export const ActivateEmployeeDialog = ({
  isOpen,
  onClose,
  employee,
}: ActivateEmployeeDialogProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutateAsync: reactivateEmployee } = useReactivateEmployee();

  const handleActivate = async () => {
    if (!employee) return;

    setIsSubmitting(true);
    try {
      await reactivateEmployee(employee.id);
      toast.success(`${employee.fullName} has been activated successfully.`);
      onClose();
    } catch (error) {
      toast.error('Failed to activate employee. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Activate Employee</DialogTitle>
          <DialogDescription>
            Are you sure you want to activate <strong>{employee?.fullName}</strong>? This will
            restore their access to the system.
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="gap-2">
          <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="button" onClick={handleActivate} disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Activate Employee
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
