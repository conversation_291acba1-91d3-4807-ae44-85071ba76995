import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface ReactivateEmployeeResponse {
  statusCode: number;
  message: string;
  data: any;
  timestamp: string;
  path: string;
  method: string;
}

const reactivateEmployeeApi = async (id: string): Promise<ReactivateEmployeeResponse> => {
  const response = await apiClient.post(`/employees/${id}/reactivate`);
  return response as unknown as ReactivateEmployeeResponse;
};

export const useReactivateEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => reactivateEmployeeApi(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees-list'] });
    },
  });
};
