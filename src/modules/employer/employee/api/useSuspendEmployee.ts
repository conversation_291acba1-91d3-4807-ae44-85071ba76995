import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';

interface SuspendEmployeePayload {
  reason: string;
}

interface SuspendEmployeeResponse {
  statusCode: number;
  message: string;
  data: any;
  timestamp: string;
  path: string;
  method: string;
}

const suspendEmployeeApi = async (
  id: string,
  payload: SuspendEmployeePayload
): Promise<SuspendEmployeeResponse> => {
  const response = await apiClient.post(`/employees/${id}/suspend`, payload);
  return response as unknown as SuspendEmployeeResponse;
};

export const useSuspendEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) =>
      suspendEmployeeApi(id, { reason }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees-list'] });
    },
  });
};
