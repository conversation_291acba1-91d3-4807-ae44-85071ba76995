'use client';

import { ReactNode } from 'react';
import { MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export interface TableActionItem<T = any> {
  label: string;
  icon?: ReactNode;
  onClick: (item: T) => void;
  className?: string;
  show?: (item: T) => boolean;
  separator?: boolean; // Add separator after this item
  disabled?: (item: T) => boolean;
  href?: string; // For link actions
}

export interface TableActionsProps<T = any> {
  item: T;
  actions: TableActionItem<T>[];
  triggerIcon?: ReactNode;
  triggerClassName?: string;
  align?: 'start' | 'end' | 'center';
}

export function TableActions<T = any>({
  item,
  actions,
  triggerIcon = <MoreHorizontal className="h-4 w-4" />,
  triggerClassName = 'h-8 w-8 p-0',
  align = 'end',
}: TableActionsProps<T>) {
  const visibleActions = actions.filter((action) => (action.show ? action.show(item) : true));

  if (!visibleActions.length) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className={triggerClassName}>
          <span className="sr-only">Open menu</span>
          {triggerIcon}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align}>
        {visibleActions.map((action, index) => {
          const isDisabled = action.disabled ? action.disabled(item) : false;

          return (
            <div key={index}>
              <DropdownMenuItem
                onClick={() => !isDisabled && action.onClick(item)}
                className={`${action.className || ''} ${isDisabled ? 'cursor-not-allowed opacity-50' : ''}`}
                disabled={isDisabled}
              >
                {action.icon && <span className="mr-2">{action.icon}</span>}
                {action.label}
              </DropdownMenuItem>
              {action.separator && index < visibleActions.length - 1 && <DropdownMenuSeparator />}
            </div>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
