'use client';

import type React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import {
  BarChart3,
  Users,
  Settings,
  Home,
  Search,
  Bell,
  CheckCircle,
  CreditCard,
  DollarSign,
  Menu,
  Calculator,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { AuthGuard } from '@/components/auth/auth-guard';
import { useState } from 'react';
import { PermissionManager } from '@/lib/permissions';
import { useAuthStore } from '@/store/auth-store';
import Image from 'next/image';
import Link from 'next/link';
import { EngagespotNotification } from '@/lib/engagespot-notification';
import { useLogout } from '@/hooks/useLogout';

// Icon mapping for navigation items
const iconMap = {
  Home,
  Users,
  CheckCircle,
  BarChart3,
  CreditCard,
  DollarSign,
  Settings,
  Calculator,
};

function AdminSidebar({ className = '' }: { className?: string }) {
  const router = useRouter();
  const pathname = usePathname();
  const permissionManager = PermissionManager.getInstance();
  const userRole = permissionManager.getUserRole();

  // Get navigation items based on user role
  const filteredNavItems = permissionManager.getNavItemsForRole();

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'Super Admin Panel';
      case 'employer':
        return 'Employer Panel';
      case 'sub_admin':
        return 'Sub Admin Panel';
      default:
        return 'User Panel';
    }
  };

  const isActiveRoute = (itemUrl: string) => {
    return pathname === itemUrl;
  };

  return (
    <div className={`border-r border-gray-200 bg-white ${className}`}>
      <div className="flex items-center gap-2 px-6 py-2">
        <div className="flex items-center justify-center rounded-lg text-white">
          <Image
            src="/logo_finwage.svg"
            alt="FinWage Logo"
            width={200}
            height={100}
            className=""
            priority
          />
        </div>
      </div>

      <nav className="p-4">
        <div className="mb-4 text-xs font-medium tracking-wider text-gray-500 uppercase">
          {getRoleDisplayName(userRole || '')}
        </div>
        <div className="space-y-1">
          {filteredNavItems.map((item) => {
            const IconComponent = iconMap[item.icon as keyof typeof iconMap];
            const isActive = isActiveRoute(item.url);

            return (
              <Link key={item.title} href={item.url}>
                <button
                  data-active={isActive}
                  className={`hover:bg-primary/10 relative my-2 flex w-full items-center gap-3 rounded-lg px-4 py-2 text-left text-sm transition-colors data-[active=true]:border-[#F74464] data-[active=true]:bg-[#F74464]/10 data-[active=true]:text-[#F74464]`}
                >
                  <IconComponent
                    className={`h-4 w-4 flex-shrink-0 ${isActive ? 'text-[#F74464]' : 'text-primary'}`}
                  />
                  <span className="truncate font-medium">{item.title}</span>
                </button>
              </Link>
            );
          })}
        </div>
      </nav>
    </div>
  );
}

function AdminUserDropdown() {
  const { userDetails } = useAuthStore();
  const { logout } = useLogout();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="flex h-auto items-center gap-2 p-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src="/placeholder.svg?height=32&width=32" />
            <AvatarFallback className="bg-primary text-white">{'SA'}</AvatarFallback>
          </Avatar>
          <div className="flex flex-col items-start text-sm">
            <span className="line-clamp-1 overflow-hidden font-medium break-words">
              {userDetails?.firstName + ' ' + userDetails?.lastName}
            </span>
            <span className="line-clamp-1 overflow-hidden text-xs break-words text-gray-500">
              {userDetails?.email}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>Profile</DropdownMenuItem>
        <DropdownMenuItem>Settings</DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={logout}>Sign out</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

interface AdminLayoutProps {
  children: React.ReactNode;
  title: string;
}

export function AdminLayout({ children, title }: AdminLayoutProps) {
  const { userDetails } = useAuthStore();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <AuthGuard allowedRoles={['super_admin']}>
      <div className="flex h-screen bg-gray-50">
        {/* Desktop Sidebar */}
        <div className="hidden w-64 flex-shrink-0 lg:block">
          <AdminSidebar className="h-full" />
        </div>

        {/* Mobile Sidebar */}
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetContent side="left" className="w-64 p-0">
            <AdminSidebar className="h-full" />
          </SheetContent>
        </Sheet>

        {/* Main Content */}
        <div className="flex min-w-0 flex-1 flex-col">
          {/* Header */}
          <header className="border-b border-gray-200 bg-white px-6 py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Sheet>
                  <SheetTrigger asChild>
                    <Button variant="ghost" size="icon" className="lg:hidden">
                      <Menu className="h-5 w-5" />
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="w-64 p-0">
                    <AdminSidebar className="h-full" />
                  </SheetContent>
                </Sheet>
                <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
              </div>

              <div className="flex items-center gap-4">
                <div className="relative hidden md:block">
                  <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <Input
                    type="search"
                    placeholder="Search..."
                    className="h-9 w-80 border-gray-200 bg-gray-50 pl-10 focus:bg-white"
                  />
                </div>
                {userDetails?.id && <EngagespotNotification userId={userDetails?.id} />}
                <AdminUserDropdown />
              </div>
            </div>
          </header>

          {/* Page Content */}
          <main className="flex-1 overflow-auto bg-white p-6">
            <div className="max-w-full">{children}</div>
          </main>
        </div>
      </div>
    </AuthGuard>
  );
}
