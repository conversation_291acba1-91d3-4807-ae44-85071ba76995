'use client';

import type React from 'react';
import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { type Role } from '@/lib/permissions';
import { useAuthWithRole } from '@/providers/UserRoleProvider';

interface AuthGuardProps {
  children: React.ReactNode;
  allowedRoles: Role[];
  requiredPermission?: string;
}

export function AuthGuard({ children, allowedRoles, requiredPermission }: AuthGuardProps) {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, role, isLoading, hasRequiredRole, isReady } = useAuthWithRole();

  useEffect(() => {
    if (isLoading) return; // Wait for role to load

    const checkAuth = () => {
      // Check if user is authenticated
      if (!isAuthenticated) {
        router.push('/');
        return;
      }

      // Check if user has required role for admin access
      if (!hasRequiredRole) {
        router.push('/');
        return;
      }

      if (!role) {
        router.push('/');
        return;
      }

      // PREVENT super_admin from accessing employer routes
      if (role === 'super_admin' && pathname.startsWith('/employer')) {
        router.push('/admin/employers');
        return;
      }

      // PREVENT employer/sub_admin from accessing admin routes
      if ((role === 'employer' || role === 'sub_admin') && pathname.startsWith('/admin')) {
        router.push('/employer/dashboard');
        return;
      }

      // Check if user role is allowed - but only redirect if it's a critical mismatch
      if (!allowedRoles.includes(role)) {
        // Only redirect if user is on a completely wrong section
        // Allow users to stay on pages they can access even if not explicitly in allowedRoles
        const isOnWrongSection =
          (role === 'super_admin' && pathname.startsWith('/employer')) ||
          ((role === 'employer' || role === 'sub_admin') && pathname.startsWith('/admin'));

        if (isOnWrongSection) {
          redirectToRoleDashboard(role);
          return;
        }
      }

      setIsAuthorized(true);
    };

    const redirectToRoleDashboard = (role: Role) => {
      switch (role) {
        case 'super_admin':
          router.push('/admin/employers');
          break;
        case 'sub_admin':
          router.push('/employer/dashboard'); // Sub-admin uses employer dashboard
          break;
        case 'employer':
          router.push('/employer/dashboard');
          break;
        default:
          router.push('/');
      }
    };

    checkAuth();
  }, [
    allowedRoles,
    requiredPermission,
    router,
    pathname,
    isAuthenticated,
    role,
    hasRequiredRole,
    isLoading,
  ]);

  if (isLoading || !isReady) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
      </div>
    );
  }

  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
}
