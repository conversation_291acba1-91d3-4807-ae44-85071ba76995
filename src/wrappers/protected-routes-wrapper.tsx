'use client';

import { useAuthStore } from '@/store/auth-store';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuthWithRole } from '@/providers/UserRoleProvider';

function ProtectedRoutesWrapper({
  children,
  isProtectedRoute,
}: {
  children: React.ReactNode;
  isProtectedRoute: boolean;
}) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);
  const { authToken } = useAuthStore();
  const { role, isLoading: roleLoading, isReady } = useAuthWithRole();

  // Wait for hydration to complete before running auth logic
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  useEffect(() => {
    // Don't run auth logic until store is hydrated
    if (!isHydrated) return;

    if (!isProtectedRoute) {
      if (authToken && isReady && role) {
        // Redirect authenticated users away from login page
        if (role === 'super_admin') {
          router.replace('/admin/employers');
        } else if (role === 'employer') {
          router.replace('/employer/dashboard');
        } else if (role === 'sub_admin') {
          router.replace('/employer/dashboard');
        }
      } else if (!authToken && !roleLoading) {
        // User is not authenticated, allow access to login page
        setIsLoading(false);
      }
    }
  }, [authToken, router, isProtectedRoute, role, isReady, roleLoading, isHydrated]);

  useEffect(() => {
    // Don't run auth logic until store is hydrated
    if (!isHydrated) return;

    if (isProtectedRoute) {
      if (!authToken) {
        router.replace('/');
      } else if (isReady) {
        // User is authenticated and role is loaded
        setIsLoading(false);
      }
    }
  }, [authToken, router, isProtectedRoute, isReady, isHydrated]);

  if (isLoading || (isProtectedRoute && roleLoading)) {
    return <div className="min-h-screen animate-pulse bg-white"></div>;
  }

  return <>{children}</>;
}

export default ProtectedRoutesWrapper;
