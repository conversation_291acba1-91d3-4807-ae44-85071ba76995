'use client';

import { useAuthStore } from '@/store/auth-store';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

function ProtectedRoutesWrapper({
  children,
  isProtectedRoute,
}: {
  children: React.ReactNode;
  isProtectedRoute: boolean;
}) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);
  const { authToken } = useAuthStore();

  // Wait for hydration to complete before running auth logic
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  useEffect(() => {
    // Don't run auth logic until store is hydrated
    if (!isHydrated) return;

    if (!isProtectedRoute) {
      if (authToken) {
        const userRole = localStorage.getItem('userRole');

        if (userRole === 'super_admin') {
          router.replace('/admin/employers');
        } else if (userRole === 'employer') {
          router.replace('/employer/dashboard');
        } else if (userRole === 'sub_admin') {
          router.replace('/employer/dashboard');
        } else {
          router.replace('/');
          localStorage.clear();
        }
      } else {
        setIsLoading(false);
      }
    }
  }, [authToken, router, isProtectedRoute, isHydrated]);

  useEffect(() => {
    // Don't run auth logic until store is hydrated
    if (!isHydrated) return;

    if (isProtectedRoute) {
      if (!authToken) {
        router.replace('/');
      } else {
        setIsLoading(false);
      }
    }
  }, [authToken, router, isProtectedRoute, isHydrated]);

  if (isLoading) {
    return <div className="min-h-screen animate-pulse bg-white"></div>;
  }

  return <>{children}</>;
}

export default ProtectedRoutesWrapper;
